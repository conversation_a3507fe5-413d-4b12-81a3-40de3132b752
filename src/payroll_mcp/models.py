"""
Pydantic models for payroll API parameters and responses.

These models provide comprehensive validation for all parameters
documented in payroll-grid-api-parameters.md and response structures
from PayrollGrid_API_Response_Documentation.md.
"""

from datetime import date
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator, model_validator


class PayrollParameters(BaseModel):
    """
    Comprehensive model for all payroll grid API parameters.

    Based on the documentation in payroll-grid-api-parameters.md,
    this model includes all possible parameters with proper validation.
    All parameters have default values to ensure robust API requests.
    """

    # Core Parameters (Required)
    type: str = Field(
        default="owners",
        description="Operation mode: 'owners', 'sums', or 'payroll_by_owner'",
        pattern="^(owners|sums|payroll_by_owner)$",
    )
    farming_year: int = Field(
        default=16, description="Farming year ID for payroll calculations", gt=0
    )

    # Date Range Parameters
    payroll_from_date: str = Field(
        default="2024-10-01",
        description="Начална дата за периода на ведомостта (YYYY-MM-DD format)",
        pattern=r"^\d{4}-\d{2}-\d{2}$",
    )
    payroll_to_date: str = Field(
        default="2025-09-30",
        description="Крайна дата за периода на ведомостта (YYYY-MM-DD format)",
        pattern=r"^\d{4}-\d{2}-\d{2}$",
    )

    # Location and Administrative Filters
    payroll_ekate: List[str] = Field(
        default_factory=lambda: [""],
        description="Търсене по ЕКАТТЕ кодове",
    )
    payroll_farming: List[str] = Field(
        default_factory=lambda: [""], description="Търсене по име на стопанство"
    )

    # Owner Type and Identification Filters
    owner_type: str = Field(
        default="0,1",
        description="Owner type filter: '0' (companies), '1' (individuals), '0,1' (both)",
        pattern=r"^(0|1|0,1)$",
    )
    owner_names: str = Field(
        default="", description="Търсене по имена на индивидуален собственик"
    )
    egn: str = Field(default="", description="Търсене по ЕГН номер")
    eik: str = Field(default="", description="Търсене по ЕИК номер")
    company_name: str = Field(default="", description="Търсене по име на компания")

    # Advanced Owner Filters
    owner_egns: List[str] = Field(
        default_factory=list,
        description="Търсене по няколко ЕГН-а номера",
    )
    company_eiks: List[str] = Field(
        default_factory=list,
        description="Търсене по няколко ЕИК-а номера",
    )

    # Representative Filters
    rep_names: str = Field(default="", description="Търсене по имена на представител")
    rep_egn: str = Field(default="", description="Търсене по ЕГН на представител")
    rep_rent_place: str = Field(
        default="",
        description="Търсене по място на получаване на рента от представител",
    )

    # Location Filters
    rent_place: str = Field(
        default="", description="Търсене по място на получаване на рента"
    )

    # Heritor Filters
    heritor_names: str = Field(default="", description="Търсене по имена на наследник")
    heritor_egn: str = Field(default="", description="Търсене по ЕГН на наследник")

    @field_validator("payroll_from_date", "payroll_to_date")
    @classmethod
    def validate_date_format(cls, v: str) -> str:
        """Validate date format and ensure it's a valid date."""
        try:
            # Parse to ensure it's a valid date
            year, month, day = map(int, v.split("-"))
            date(year, month, day)
            return v
        except (ValueError, TypeError) as e:
            raise ValueError(
                f"Invalid date format. Expected YYYY-MM-DD, got: {v}"
            ) from e

    @model_validator(mode="after")
    def validate_date_range(self) -> "PayrollParameters":
        """Ensure payroll_to_date is after payroll_from_date."""
        from_date = date.fromisoformat(self.payroll_from_date)
        to_date = date.fromisoformat(self.payroll_to_date)

        if to_date <= from_date:
            raise ValueError(
                f"payroll_to_date ({self.payroll_to_date}) must be after "
                f"payroll_from_date ({self.payroll_from_date})"
            )

        return self

    @model_validator(mode="after")
    def validate_owner_filters(self) -> "PayrollParameters":
        """Validate owner filter combinations."""
        # If owner_egns is provided and not empty, it overrides egn
        if self.owner_egns and self.egn:
            # Log a warning that egn will be ignored
            pass

        # If company_eiks is provided and not empty, it overrides eik
        if self.company_eiks and self.eik:
            # Log a warning that eik will be ignored
            pass

        return self

    def to_api_params(self) -> Dict[str, Any]:
        """
        Convert to the parameter format expected by the external API.

        This method handles the transformation from the MCP tool parameters
        to the format expected by the payroll grid API. All parameters now
        have default values, so all fields are included in the API request.
        """
        # Include all parameters since they all have default values
        params = self.model_dump()
        return params


class JsonRpcRequest(BaseModel):
    """JSON-RPC 2.0 request model for external API calls."""

    jsonrpc: str = Field(default="2.0", description="JSON-RPC version")
    method: str = Field(description="Method to call")
    params: List[Any] = Field(description="Method parameters")
    id: Union[str, int] = Field(description="Request ID")


class JsonRpcResponse(BaseModel):
    """JSON-RPC 2.0 response model from external API."""

    jsonrpc: str = Field(description="JSON-RPC version")
    id: Union[str, int] = Field(description="Request ID")
    result: Optional[Dict[str, Any]] = Field(None, description="Success result")
    error: Optional[Dict[str, Any]] = Field(None, description="Error details")

    @model_validator(mode="after")
    def validate_result_or_error(self) -> "JsonRpcResponse":
        """Ensure either result or error is present, but not both."""
        if self.result is not None and self.error is not None:
            raise ValueError("Response cannot have both result and error")

        if self.result is None and self.error is None:
            raise ValueError("Response must have either result or error")

        return self


class PayrollGridResponse(BaseModel):
    """
    Model for the payroll grid API response structure.

    Based on PayrollGrid_API_Response_Documentation.md
    """

    rows: List[Dict[str, Any]] = Field(description="Array of payroll records")
    total: int = Field(description="Total count of all results")
    footer: List[Dict[str, Any]] = Field(
        description="Array containing summary/totals data"
    )


class ToolResult(BaseModel):
    """Result model for MCP tool responses."""

    success: bool = Field(description="Whether the operation was successful")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error message if failed")

    @classmethod
    def success_result(cls, data: Dict[str, Any]) -> "ToolResult":
        """Create a successful result."""
        return cls(success=True, data=data)

    @classmethod
    def error_result(cls, error: str) -> "ToolResult":
        """Create an error result."""
        return cls(success=False, error=error)
